package lotwf

import (
	"anno/api/client"
	"anno/internal/biz"
	"anno/internal/mq"
	"anno/workflow/common"
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/wf/wfutil"
	"go.temporal.io/sdk/activity"
)

func (o *Activities) LotGetDataState(ctx context.Context, lotID int64) (string, error) {
	lot, err := o.lotbiz.GetByID(ctx, lotID, false)
	if err != nil {
		return "", fmt.Errorf("failed to get lot: %w", err)
	}
	if lot.State == biz.LotStateCanceled {
		return lot.State.String(), nil
	}

	ctx = client.NewCtxUseSvcAccount(ctx)
	data, err := client.GetData(ctx, lot.DataUid)
	if err != nil {
		if errors.IsNotFound(err) {
			return "", o.onDataNotFound(ctx, lot, err)
		}
		return "", fmt.Errorf("failed to get data: %w", err)
	}

	if data.State == client.DataStateReady.ToPb() {
		updates := &biz.Lot{
			ID:       lotID,
			DataSize: data.Size,
			DataType: data.Type.String(),
		}
		flds := []string{biz.LotSfldDataSize.String(), biz.LotSfldDataType.String()}
		if series := data.Source.IsFrameSeries; series {
			updates.IsFrameSeries = series
			flds = append(flds, biz.LotSfldIsFrameSeries.String())
		}
		_, err = o.lotbiz.Update(ctx, updates, field.NewMask(flds...))
		if err != nil {
			return "", fmt.Errorf("failed to set lot data size: %w", err)
		}
		if lot.OrderID > 0 {
			_, err = o.orderbiz.Update(ctx, &biz.Order{
				ID:    lot.OrderID,
				State: biz.OrderStateOngoing,
				Size:  data.Size,
			}, field.NewMask(biz.OrderSfldState.String(), biz.OrderSfldSize.String()))
			if err != nil {
				return "", fmt.Errorf("failed to udpate order state: %w", err)
			}
		}
	}
	return data.State.String(), nil
}

func (o *Activities) onDataNotFound(ctx context.Context, lot *biz.Lot, errIn error) error {
	_, err := o.lotbiz.Update(ctx, &biz.Lot{
		ID:       lot.ID,
		State:    biz.LotStateCanceled,
		Error:    *serial.New(&anno.Error{Reason: "data-deleted"}),
		HasError: true,
	}, field.NewMask(biz.LotSfldState.String(), biz.LotSfldError.String(), biz.LotSfldHasError.String()))
	if err != nil {
		return fmt.Errorf("failed to udpate lot state: %w", err)
	}
	if lot.OrderID > 0 {
		_, err = o.orderbiz.Update(ctx, &biz.Order{
			ID:    lot.OrderID,
			State: biz.OrderStateCanceled,
			Error: "data-deleted",
		}, field.NewMask(biz.OrderSfldState.String(), biz.OrderSfldError.String()))
		if err != nil {
			return fmt.Errorf("failed to udpate order state: %w", err)
		}
	}
	return wfutil.NewNonRetryableError("data not found", errIn)
}

func (o *Activities) LotCreateJobs(ctx context.Context, lotID int64) error {
	fmt.Println("---> Lot Create Jobs begin: ", lotID)
	lot, err := o.lotbiz.GetByID(ctx, lotID, false)
	if err != nil {
		return fmt.Errorf("failed to get lot: %w", err)
	}
	if lot.State == biz.LotStateCanceled {
		return wfutil.NewNonRetryableError("lot is cancelled", nil)
	}

	ctx = client.NewCtxUseSvcAccount(ctx)
	// find last element idx in dataset
	elemIdx := 0
	jobIdx := wfutil.GetActivtityStartIndex(ctx, &elemIdx)
	fmt.Println("---> init jobIdx: ", jobIdx, ", job size: ", int(lot.JobSize))
	// o.log.Infof("CreateJobs: enter: jobIdx=%v, elemIdx=%v", jobIdx, elemIdx)
	for ; ; jobIdx++ {
		fmt.Println("---> jobIdx: ", jobIdx, "elemIdx: ", elemIdx)

		// ======================= DEBUGGING CODE START =======================
		//spew.Dump(ctx)
		fmt.Printf("=== Context Timeout Debug ===\n")
		if deadline, ok := ctx.Deadline(); ok {
			// 如果 ok 是 true，说明 context 明确设置了截止时间
			remaining := time.Until(deadline)
			fmt.Printf("!!!!!! Context has a deadline. Time remaining: %v\n", remaining)
		} else {
			fmt.Printf("!!!!!! Context has NO deadline.\n")
		}
		// ======================= DEBUGGING CODE END =========================

		// 为GetDataElements调用设置更长的超时时间
		//elemCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
		//defer cancel()
		start := time.Now()
		fmt.Printf("---> Starting GetDataElements call at: %v\n", start)
		elems, err := client.GetDataElements(ctx, lot.DataUid, elemIdx, int(lot.JobSize))
		duration := time.Since(start)
		fmt.Printf("---> GetDataElements call completed in: %v\n\n", duration)
		fmt.Println("---> get data elements count: ", len(elems), ", err: ", err)
		if err != nil {
			if errors.IsNotFound(err) {
				return o.onDataNotFound(ctx, lot, err)
			}
			return fmt.Errorf("failed to query elements of %v from %v: %w", lot.DataUid, elemIdx, err)
		}
		if len(elems) == 0 {
			break
		}
		fmt.Println("---> elems len: ", len(elems))
		if err := o.createJob(ctx, lot, elems, jobIdx); err != nil {
			return fmt.Errorf("failed to create job for lot %v: %w", lot.GetUid(), err)
		}
		elemIdx += len(elems)
		activity.RecordHeartbeat(ctx, jobIdx, elemIdx) // Report progress.
	}
	// o.log.Infof("CreateJobs: exit: jobIdx=%v, elemIdx=%v", jobIdx, elemIdx)
	lot.JobCount = jobIdx
	_, err = o.lotbiz.Update(ctx, lot, field.NewMask(biz.LotSfldJobCount.String()))
	if err != nil && !errors.IsNotFound(err) {
		return fmt.Errorf("failed to update lot %v: %w", lot.GetUid(), err)
	}

	// update total_jobs to lotphase
	lotphases, err := o.lotbiz.Repo().LoadPhases(ctx, lotID, 0)
	if err != nil {
		return fmt.Errorf("failed to get lotphase: %w", err)
	}
	for _, lotphase := range lotphases {
		if lotphase.Quota.E.NoLimit() {
			continue
		}

		lotphase.Quota.E.WithJobCount(lot.JobCount)
		_, err := o.lotbiz.Repo().UpdatePhase(ctx, lotphase, field.NewMask(biz.LotphaseSfldQuota.String()))
		if err != nil {
			return fmt.Errorf("failed to update lotphase %v: %w", lotphase.ID, err)
		}
	}
	fmt.Println("Lot Create Jobs end: ", lotID)

	return nil
}

func (o *Activities) createJob(ctx context.Context, lot *biz.Lot, elems []*anno.Element, jobIdx int) error {
	fmt.Println("---> create job")
	// TODO: create sub jobs in a tx according to lot.ally.Ontologies.E.Groups
	newID := kid.NewID()
	job := &biz.Job{
		ID:       newID,
		LotID:    lot.ID,
		IdxInLot: int32(jobIdx),
		Subtype:  "",
		ElemsCnt: int32(len(elems)),
		State:    biz.JobStateUnstart,
		Phase:    1, // phase number starts from 1
		InsCnt:   0,
		Ally: &biz.Jobally{
			ID:       newID,
			Elements: elems,
		},
	}

	jobelems := lo.Map(elems, func(v *anno.Element, _ int) *biz.Jobelem {
		return &biz.Jobelem{
			JobID:    newID,
			LotID:    lot.ID,
			DataUid:  lot.DataUid,
			ElemIdx:  v.Index,
			ElemName: v.Name,
		}
	})
	_, err := o.jobelemrepo.BatchCreate(ctx, jobelems)
	if err != nil {
		return fmt.Errorf("failed to batch create jobelems: %w", err)
	}

	job.Ally.RawdataParams = *serial.New(&biz.RawdataParams{})
	if biz.EnableJobCamParams() {
		camParams := aggCamParams(elems)
		job.Ally.RawdataParams.E.CamParams = camParams
	}

	uploaded, err := job.UploadElements(ctx,
		&biz.ElementData{Elements: elems, CamParams: job.Ally.RawdataParams.E.CamParams}, lot.OrgUid, lot.GetUid())
	if err != nil {
		return fmt.Errorf("failed to upload elements: %w", err)
	}
	if uploaded {
		job.Ally.Elements = []*anno.Element{}
		job.Ally.RawdataParams = biz.RawdataParamsType{}
	}

	hasAnnos := lo.SomeBy(elems, func(v *anno.Element) bool { return v.Anno != nil })
	if hasAnnos {
		var jobAttrs []*anno.AttrAndValues
		job.InsCnt = 0
		job.Ally.Annotations = *serial.New(&anno.JobAnno{
			JobIndex:          job.IdxInLot,
			NeedInterpolation: lot.IsFrameSeries,
			ElementAnnos: lo.Map(elems, func(v *anno.Element, idx int) *anno.ElementAnno {
				if v.Anno == nil {
					return &anno.ElementAnno{
						Name: v.Name,
						// TODO Index need to be filled in.
					}
				}

				// remove trackID prefix.
				// Be aware that if job size is not the same as the one in the lot
				// where the annotations were made, trackID will be probably incorrect
				// after prefix removal.
				const prefix = "konv:job"
				v.Anno.InsCnt = 0
				// TODO v.Anno.Index might need to be fix when the elements in data are more than the elements in annos.
				for _, e := range v.Anno.RawdataAnnos {
					stripInterpolated := biz.ShouldStripInterpolatedIns()
					if lot.IsFrameSeries && stripInterpolated {
						// delete interpolated annotations
						objs := make([]*anno.Object, 0, len(e.Objects))
						for _, d := range e.Objects {
							if d.Source != anno.Object_Source_interpolation {
								objs = append(objs, d)
							}
						}
						e.Objects = objs
					}
					for _, d := range e.Objects {
						if strings.HasPrefix(d.TrackId, prefix) {
							if _, after, found := strings.Cut(d.TrackId, "."); found {
								d.TrackId = after
							}
						}
					}
					v.Anno.InsCnt += int32(len(e.Objects))
				}

				if idx == 0 || len(jobAttrs) > 0 {
					var frameAttrs []*anno.AttrAndValues
					for _, attr := range v.Anno.Attrs {
						const prefix = "konv:job_attr:"
						if strings.HasPrefix(attr.Name, prefix) {
							if idx == 0 { // only collect job attrs in the first elem since job attrs appear in each elem
								jobAttrs = append(jobAttrs, &anno.AttrAndValues{
									Name:   attr.Name[len(prefix):],
									Values: attr.Values,
								})
							}
						} else {
							frameAttrs = append(frameAttrs, attr)
						}
					}
					v.Anno.Attrs = frameAttrs
				}
				job.InsCnt += v.Anno.InsCnt
				return v.Anno
			}),
		})
		job.Ally.Annotations.E.Attrs = jobAttrs
		job.Ally.Annotations.E.InsCnt = job.InsCnt
		lo.ForEach(elems, func(v *anno.Element, _ int) { v.Anno = nil })

		// upload annos
		uploaded, err := job.UploadAnnotations(ctx,
			&biz.AnnotationData{
				ElementAnnos: job.Ally.Annotations.E.GetElementAnnos(),
				JobAttrs:     job.Ally.Annotations.E.GetAttrs(),
			},
			lot.OrgUid, lot.GetUid(),
		)
		if err != nil {
			return err
		}
		if uploaded {
			job.Ally.Annotations = biz.Annotations{}
		}
	}

	_, err = o.jobbiz.Create(ctx, job)
	return err
}

func (o *Activities) LotInitJobs(ctx context.Context, lotID int64) error {
	fmt.Println("---> lot init jobs")
	const pagesz = 100
	ctx = client.NewCtxUseSvcAccount(ctx)
	startIdx := wfutil.GetActivtityStartIndex(ctx)
	lot, err := o.lotbiz.GetByID(ctx, lotID, true)
	if err != nil {
		return fmt.Errorf("failed to get lot: %w", err)
	}
	if lot.State == biz.LotStateCanceled {
		return wfutil.NewNonRetryableError("lot is cancelled", nil)
	}
	err = o.lotbiz.RenewSampleBits(ctx, lot)
	if err != nil {
		return fmt.Errorf("failed to generate samplebits: %w", err)
	}

	fmt.Println("start idx: ", startIdx)
	for i := startIdx; ; {
		// query jobs
		jobs, err := o.jobbiz.List(ctx, &biz.JobListFilter{LotID: lotID}, biz.Pager{Pagesz: pagesz, Page: i / pagesz})
		if err != nil {
			return fmt.Errorf("failed to list jobs: %w", err)
		}
		for j, job := range jobs {
			// in case the activity is retried due to errors, skip jobs, that we've already done with, in the first batch query.
			if j < i%pagesz {
				continue
			}

			for int(job.Phase) <= lot.PhaseCount && !lot.Ally.IsJobSampled(int(job.Phase), int(job.IdxInLot)) {
				job.Phase++
			}
			if job.Phase > 1 { // this check is not necessary, however, it can reduce unnecessary queries
				if int(job.Phase) > lot.PhaseCount {
					job.State = biz.JobStateFinished
				}
				job, err = o.jobbiz.Update(ctx, job, field.NewMask(biz.JobSfldPhase, biz.JobSfldState))
				if err != nil {
					return fmt.Errorf("failed to update job phase: %w", err)
				}
				if job.State == biz.JobStateFinished {
					ev := &common.Event{
						Event: common.EvtJobCompleted,
						JobID: job.ID,
					}
					err = SignalLotWf(ctx, lotID, ev)
					if err != nil {
						return err
					}
				}
				// TODO: signal coordJobWorkflow if the subjob is at the merge point or last phase
			}

			activity.RecordHeartbeat(ctx, i) // Report progress.
			i++
		}

		if len(jobs) < pagesz {
			return nil
		}
	}
}

// LotFillExecutors sets all members of the phase execteam as executors for each phase
func (o *Activities) LotFillExecutors(ctx context.Context, lotID int64) error {
	if !cast.ToBool(os.Getenv("AUTO_FILL_EXECUTORS")) {
		return nil
	}

	ctx = client.NewCtxUseSvcAccount(ctx)
	startIdx := wfutil.GetActivtityStartIndex(ctx)
	lot, err := o.lotbiz.GetByID(ctx, lotID, false)
	if err != nil {
		return fmt.Errorf("failed to get lot: %w", err)
	}
	if lot.State == biz.LotStateCanceled {
		return wfutil.NewNonRetryableError("lot is cancelled", nil)
	}

	teams := make(map[string][]string, lot.PhaseCount)
	for i := startIdx; i < len(lot.Phases); i++ {
		phase := lot.Phases[i]
		if phase.Execteam == "" {
			continue
		}
		uids, ok := teams[phase.Execteam]
		if !ok {
			users, err := client.GetTeamMembers(ctx, phase.Execteam)
			if err != nil {
				return fmt.Errorf("failed to get team members: %w", err)
			}
			uids = lo.Map(users, func(u *client.User, _ int) string { return u.Uid })
			teams[phase.Execteam] = uids
		}
		if len(uids) > 0 {
			err = o.lotbiz.AddPhaseExecutors(ctx, phase, phase.Execteam, uids)
			if err != nil {
				return fmt.Errorf("failed to add phase executors: %w", err)
			}
		}

		activity.RecordHeartbeat(ctx, i) // Report progress.
	}
	return nil
}

func (o *Activities) LotMarkJobReady(ctx context.Context, lotID int64) error {
	ctx = client.NewCtxUseSvcAccount(ctx)
	lot := &biz.Lot{
		ID:       lotID,
		JobReady: true,
	}
	lot, err := o.lotbiz.Update(ctx, lot, field.NewMask("job_ready"))
	if err != nil {
		return fmt.Errorf("failed to update lot state: %w", err)
	}

	err = mq.PublishEvt(ctx, biz.EvtTypeAnnoLot, biz.EvtSubtypeStart, map[string]any{
		"uid":       lot.GetUid(),
		"job_count": lot.JobCount,
		"data_type": lot.DataType,
		"data_size": lot.DataSize,
	})
	if err != nil {
		return fmt.Errorf("failed to publish lot event: %w", err)
	}

	phases, err := o.lotbiz.Repo().LoadPhases(ctx, lotID, 0)
	if err != nil {
		return fmt.Errorf("failed to load lot phases: %w", err)
	}
	err = mq.PublishEvt(ctx, biz.EvtTypeAnnoLotphase, biz.EvtSubtypeBatchCreate, phases)
	if err != nil {
		return fmt.Errorf("failed to publish lotphase event: %w", err)
	}

	return nil
}
